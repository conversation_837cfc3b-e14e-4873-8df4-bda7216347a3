# How to set up a local development environment with Docker

## Install Docker
If you are using a Mac, you can install Docker for Mac [here](https://docs.docker.com/docker-for-mac/install/).
If you are using Windows, you can install Docker for Windows [here](https://docs.docker.com/docker-for-windows/install/).
If you are on Linux, you can install Docker for Linux [here](https://docs.docker.com/docker-for-linux/install/).


## Install docker-compose
Install docker-compose [here](https://docs.docker.com/compose/install/)

## Docker-compose configuration/deployment
There is a docker-compose.yaml file in the root of the project.
This uses a customized docker image for the project (located in /docker/dev).
This image mounts the local file system in the container. 
So code changes will be reflected in the container automatically.

## Prepare the server-base docker image
Build the dockerfile in /docker/server-base/v3 with the following command (command must be run from the folder where the dockerfile is located):

```docker build . -t wavo-server-base:v3-dev-local``` 


Build the dockerfile in /docker/dev-chrome-base (copied from chrome-base/v2) with the following command (command must be run from the folder where the dockerfile is located):

```docker build . -t wavo-chrome-base:v2-dev-local```

## Build reacher image

Move to the /docker/reacher/check-if-email-exists folder and build the docker image with the following command (command must be run from the folder where the dockerfile is located):

``` docker build -f backend/Dockerfile.new -t wavo-reacher:v1-dev-local .```

// reacher proxy logs: `journalctl -u danted`

## User the docker-compose command to build the needed images
To build the images, use the command (command must be run from the project root):

```docker compose build app```

## Or build a production mirror docker file to test locally

```docker compose build -f=docker-compose-static.yaml app```


## Mysql
If you want to use a mysql through docker, you need to launch the mysql container with the command:

```docker-compose up -d mysql```

Otherwise, you can use a mysql server on the host machine.


## Env file changes
The hosts mentioned in the env file must be the names of the docker-compose services. For example

```
REDIS_HOST=redis
ELASTICSEARCH_HOST=elasticsearch
```

The host machine is reached with the address `host.docker.internal`. 
To connect to a service on the host machine (for example, mysql),  you can use the following env setting:

```DB_READ_HOST=host.docker.internal```


## Launch the app
To launch the app, use the command:

```docker-compose up -d app```

This will also launch redis and elasticsearch containers.

## Launch emailengine
To launch emailengine, use the command:

```docker-compose up -d emailengine```

This will also launch the app, redis and elasticsearch containers if not already running.

## How to start Horizon
To have better control, we don't start Horizon automatically through supervisord.
You need to ssh into the app container and start horizon from the cli:

```
docker exec -it wavo_app_1 bash
php artisan horizon
```

## How to shut down all containers
To shut down all containers, use the command:

```docker-compose down```


## Windows specific
If using docker on Windows, you will need to first install WSL2 (Windows subsystem for Linux).
If the dev folder that is mounted in the container, is on the windows file system, the app may run slowly.
It is advised to copy the project to a folder on the Linux subsystem file system and build the containers from there.
If using PhpStorm or similar IDE, it is possible to keep the code on the Windows file system and instruct the IDE to sync the files to the Linux file system.

