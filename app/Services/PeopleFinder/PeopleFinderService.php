<?php

namespace App\Services\PeopleFinder;

use App\Models\StoreLeads\Domain;
use App\Services\EmailEnrichmentService;
use App\Services\EmailVerifierService;
use App\Services\MerchantNameFinderService;
use App\Services\PeopleFinder\Crawler\CrawlerManager;
use App\Services\PeopleFinder\Helpers\EmailPatternDetector;
use App\Services\PeopleFinder\Helpers\SerperPeopleExtractor;
use App\Services\PeopleFinder\Helpers\SlugGenerator;
use App\Services\PeopleFinder\Helpers\DirectoryFetcher;
use App\Services\PeopleFinder\Helpers\OpenFetcher;
use App\Services\PeopleFinder\Helpers\MetadataParser;
use App\Services\PeopleFinder\Helpers\ExternalHandlers;
use App\Services\SeniorityRankingService;
use App\Services\SerperClient;
use Illuminate\Support\Facades\Log;

class PeopleFinderService
{
    private const LLM_MODEL = 'deepseek-chat';
    private const SERPER_PRICING_TIER = 'standard';

    private SeniorityRankingService $seniorityRanker;

    public function __construct()
    {
        $this->seniorityRanker = new SeniorityRankingService();
    }

    /**
     * Entry point for Domain model objects
     */
    public function findPeopleFromDomain(Domain $domain): array
    {
        // Extract the root domain name, cleaning up https:// and www., paths, query strings, etc.
        $domainName = $domain->name;

        // Get company name from domain object or try to find it
        if (!empty($domain->merchant_name)) {
            $companyName = $domain->merchant_name;
        } else {
            $companyName = $domain->domainData->data['merchant_name'] ?? null;
        }
        if (!empty($companyName)) {
            $companyName = $this->cleanCompanyName($companyName);
        }

        // Gather metadata from Domain model and its relationships
        $metadata = [
            'domain_id' => $domain->id,
            'company_name' => $companyName,
            'categories' => $domain->categories->pluck('name')->toArray(),
            'platform' => $domain->platform ? $domain->platform->name : null,
            'estimated_sales' => $domain->estimated_sales['name'] ?? null,
            'country' => $domain->country ? $domain->country->name : null,
            'company_size' => $domain->employee_count['name'] ?? null,
            'language' => $domain->language ? $domain->language->name : null,
            'product_count' => $domain->product_count['name'] ?? null,
            'region' => $domain->region ?? null
        ];

        $this->validateInputs($domainName, $companyName);
        return $this->findPeople($domainName, $metadata);
    }

    /**
     * Entry point for URLs
     */
    public function findPeopleFromUrl(string $url): array
    {
        // Clean and extract the domain
        $domainName = $url;
        $companyName = null;

        // Try to find the company name
        // First check if we can find an existing domain record
        $domain = Domain::where('name', $domainName)->first();

        if ($domain && !empty($domain->merchant_name)) {
            $companyName = $this->cleanCompanyName($domain->merchant_name);
        } else {
            // Create temporary domain object for merchant finder
            $tempDomain = new Domain();
            $tempDomain->name = $domainName;
            $tempDomain->domainData = new \App\Models\StoreLeads\DomainData();
            $tempDomain->domainData->data = ['contact_info' => []];

            $merchantFinder = new MerchantNameFinderService($tempDomain);
            $result = $merchantFinder->searchMerchantName();

            if (!empty($result['name']) && $result['score'] >= MerchantNameFinderService::PASSING_SCORE) {
                $companyName = $this->cleanCompanyName($result['name']);
            }
        }

        $this->validateInputs($domainName, $companyName);
        return $this->findPeople($domainName);
    }


    /**
     * Entry point for company name input
     */
    public function findPeopleFromCompany(string $companyName): array
    {
        $companyName = $this->cleanCompanyName($companyName);
        $domainName = null;

        // Try to find domain from company name using a search
        $query = sprintf('"%s" official website', $companyName);
        $results = SerperClient::search($query);

        if (!empty($results['organic'])) {
            foreach ($results['organic'] as $result) {
                $link = $result['link'] ?? '';
                if (!empty($link) && !$this->isSearchEngine($link) && !$this->isSocialMedia($link)) {
                    try {
                        $domainName = $link;
                        break;
                    } catch (\InvalidArgumentException $e) {
                        // If this link fails to normalize, try the next one
                        continue;
                    }
                }
            }
        }

        $metadata = [
            'company_name' => $companyName,
        ];

        $this->validateInputs($domainName, $companyName);
        return $this->findPeople($domainName, $metadata);
    }

    // Main entry point
    public function findPeople(string $domainName, array $metadata = []): array
    {
        Log::debug("Start findPeople for $domainName");
        // Create the context
        $context = new PeopleFinderContext($domainName, $metadata);

        // Step 1: Website Crawling
        $context->metricsCollector->startProcess('crawler');
        $crawler = new CrawlerManager();
        $crawler->crawl($context);
        $context->metricsCollector->endProcess('crawler');

        // STEP 2: Find the Email Pattern
        EmailPatternDetector::detectAndSetPattern($context);

        // STEP 3: Try to Get People Without Using Serper
        $context->metricsCollector->startProcess('free_discovery');
        $this->freePeopleDiscovery($context);
        $context->metricsCollector->endProcess('free_discovery');

        if ($context->highConfidenceExecutivesCount() === 0) {
            // STEP 4: Run Controlled Serper Query to Get Decision-Makers
            $context->metricsCollector->startProcess('serper');
            $serperService = new SerperPeopleExtractor(self::LLM_MODEL);
            $serperService->searchWithSerper($context);
            $context->metricsCollector->endProcess('serper');
        }

        // STEP 5: Build Likely Email Addresses from Names + Pattern
        $this->applyEmailPatternToPeople($context);

        // STEP 6: Enrich with Public Info (LinkedIn, social, etc)
        $context->metricsCollector->startProcess('enrichment', $context);
        $this->enrichPeopleWithPublicData($domainName, $context);
        $context->metricsCollector->endProcess('enrichment', $context);

        // Step 7: Verify Emails
        $this->verifyEmails($context);

        // STEP 8: Score contacts
        $this->applyCrossReferenceBoosts($context);
        $context->scoreContacts();

        // Finalize metrics
        $context->metricsCollector->finalizeContactMetrics()->save();

        // Convert contacts to the expected return format
        return $this->formatContactsForReturn($context);
    }

    // Pulls exec names from open sources (Wikidata + Wikipedia via ExternalHandlers)
    public function freePeopleDiscovery(PeopleFinderContext $context): void
    {
        $domain = $context->domain;
        $companyName = $context->metadata['company_name'] ?? null;

        if (empty($companyName)) {
            return;
        }

        $directorySources = [
            // Level 1 – Totally open/public sources
            'theorg' => [
                'level' => 1,
                'access' => 'open',
                'url_template' => 'https://theorg.com/org/{slug}',
                'slug_type' => 'domain',
            ],
            'wikipedia' => [
                'level' => 1,
                'access' => 'handler',
                'handler' => 'lookupWikipedia',
            ],
            'wikidata' => [
                'level' => 1,
                'access' => 'handler',
                'handler' => 'lookupWikidata',
            ],

            // Level 2 – Directory sources (semi-safe for metadata fetch only)
            'owler' => [
                'level' => 2,
                'access' => 'throttled',
                'url_template' => 'https://www.owler.com/company/{slug}',
                'slug_type' => 'name_slug',
            ],
            'theofficialboard' => [
                'level' => 2,
                'access' => 'throttled',
                'url_template' => 'https://www.theofficialboard.com/company/{slug}',
                'slug_type' => 'name_slug',
            ],
            'crunchbase' => [
                'level' => 2,
                'access' => 'throttled',
                'url_template' => 'https://www.crunchbase.com/organization/{slug}',
                'slug_type' => 'name_slug',
            ],
            'rocketreach' => [
                'level' => 2,
                'access' => 'blocked', // handled via Serper in Step 3
            ],
        ];

        foreach ($directorySources as $source => $config) {
            switch ($config['access']) {
                case 'open':
                    $slug = SlugGenerator::generate($domain, $companyName, $config['slug_type']);
                    $url = str_replace('{slug}', $slug, $config['url_template']);
                    OpenFetcher::fetchAndParse($url, $source, $context);
                    if (!empty($context->contacts)) {
                        return;
                    }
                    break;

                case 'throttled':
                    $slug = SlugGenerator::generate($domain, $companyName, $config['slug_type']);
                    $url = str_replace('{slug}', $slug, $config['url_template']);
                    $response = DirectoryFetcher::fetchMetadata($url);
                    if ($response['ok']) {
                        $contactCountBefore = count($context->contacts);
                        MetadataParser::extractPerson($response['metadata'], $source, $context);
                        if (count($context->contacts) > $contactCountBefore) {
                            return;
                        }
                    }
                    break;

                case 'handler':
                    if (isset($config['handler']) && method_exists(ExternalHandlers::class, $config['handler'])) {
                        $handler = $config['handler'];
                        $contactCountBefore = count($context->contacts);
                        ExternalHandlers::$handler($domain, $companyName, $context);
                        if (count($context->contacts) > $contactCountBefore) {
                            return;
                        }
                    }
                    break;

                case 'blocked':
                    break; // Will be handled in Step 3 (Serper)
            }
        }
    }

    /**
     * Apply email pattern to contacts without emails
     *
     * @param PeopleFinderContext $context
     * @return void
     */
    protected function applyEmailPatternToPeople(PeopleFinderContext $context): void
    {
        $pattern = $context->emailPattern;
        $domain = $context->domain;

        if (empty($pattern)) {
            return;
        }

        // Set up tracking for pattern-generated emails
        $emailPatternSource = "email_pattern_application";
        $emailsGenerated = 0;
        $patternsGeneratedContacts = [];

        foreach ($context->contacts as &$contact) {
            $existingEmail = $contact['email'] ?? null;
            $existingSource = $contact['email_source'] ?? 'extracted';

            // Apply pattern if:
            // 1. No email exists, or
            // 2. Email is masked (don't try to use masked characters)
            if (empty($existingEmail) || $existingSource === 'masked') {
                // Skip if contact doesn't have first and last name
                if (empty($contact['first_name']) || empty($contact['last_name'])) {
                    continue;
                }

                $firstName = strtolower($contact['first_name']);
                $lastName = strtolower($contact['last_name']);

                // Generate email based on pattern
                $email = null;
                switch ($pattern) {
                    case 'first.last':
                        $email = $firstName . '.' . $lastName . '@' . $domain;
                        break;
                    case 'firstlast':
                        $email = $firstName . $lastName . '@' . $domain;
                        break;
                    case 'first':
                        $email = $firstName . '@' . $domain;
                        break;
                    case 'last':
                        $email = $lastName . '@' . $domain;
                        break;
                    case 'first.l':
                        $email = $firstName . '.' . substr($lastName, 0, 1) . '@' . $domain;
                        break;
                    case 'f.last':
                        $email = substr($firstName, 0, 1) . '.' . $lastName . '@' . $domain;
                        break;
                    case 'flast':
                        $email = substr($firstName, 0, 1) . $lastName . '@' . $domain;
                        break;
                    case 'firstl':
                        $email = $firstName . substr($lastName, 0, 1) . '@' . $domain;
                        break;
                    case 'first_last':
                        $email = $firstName . '_' . $lastName . '@' . $domain;
                        break;
                    case 'f_last':
                        $email = substr($firstName, 0, 1) . '_' . $lastName . '@' . $domain;
                        break;
                    case 'first_l':
                        $email = $firstName . '_' . substr($lastName, 0, 1) . '@' . $domain;
                        break;
                    case 'last.f':
                        $email = $lastName . '.' . substr($firstName, 0, 1) . '@' . $domain;
                        break;
                    case 'f-last':
                        $email = substr($firstName, 0, 1) . '-' . $lastName . '@' . $domain;
                        break;
                    default:
                        // Default to first.last
                        $email = $firstName . '.' . $lastName . '@' . $domain;
                }

                // Update contact with generated email
                $contact['email'] = $email;
                $contact['email_source'] = 'pattern';

                $emailsGenerated++;
                $patternsGeneratedContacts[] = $contact;
            }
        }

        // Record email pattern application metrics
        $context->metricsCollector->recordEmailPatternApplication(
            $emailsGenerated,
            $patternsGeneratedContacts
        );
    }

    /**
     * Apply cross-reference boosts to contacts found across multiple sources
     * Our added contacts can have cross-references that fall into any of the following cases:
     * 1. Contact is discovered through step 1 (web crawling) - not found anywhere else
     * 2. Contact is discovered through steps 3 or 4 - no mention in website
     * 3. Contact is discovered through steps 3 or 4 - we then also find it in the website
     * 4. Contact is discovered through step 1 - we then also find it in steps 3 or 4 and maybe this
     *
     * @param PeopleFinderContext $context
     * @return void
     */
    protected function applyCrossReferenceBoosts(PeopleFinderContext $context): void
    {
        // Skip if no crawled pages available
        if (empty($context->crawledPages)) {
            return;
        }

        // Track website mentions for all contacts
        foreach ($context->contacts as $index => $contact) {
            // Skip if we don't have a full name
            if (empty($contact['first_name']) || empty($contact['last_name'])) {
                continue;
            }

            $fullName = $contact['first_name'] . ' ' . $contact['last_name'];
            $websiteMentions = [];

            // Check mentions across all crawled pages
            foreach ($context->crawledPages as $pageIndex => $page) {
                if (stripos($page['html'], $fullName) !== false) {
                    $websiteMentions[] = $page['url'];
                }
            }

            // No website mentions, no cross-reference to add
            if (empty($websiteMentions)) {
                // Case 2: Contact from external source only - no website mentions, no action needed
                continue;
            }

            // Determine the primary source based on enrichment_source
            $enrichmentSource = $contact['enrichment_source'] ?? '';
            $isWebsiteSource = (strpos($enrichmentSource, 'website') !== false);
            $isExternalSource = !$isWebsiteSource;

            // Determine the source_url domain to check if it's from website or external
            $sourceUrlHost = parse_url($contact['source_url'] ?? '', PHP_URL_HOST) ?? '';
            $domainHost = parse_url($context->domainUrl, PHP_URL_HOST) ?? '';
            $isFromWebsiteCrawl = ($sourceUrlHost === $domainHost || strpos($sourceUrlHost, $domainHost) !== false);

            // Case 1: Contact from website crawling only with multiple mentions
            if ($isWebsiteSource && count($websiteMentions) > 1) {
                $context->contacts[$index]['cross_references'] = [
                    'type' => 'website_multiple',
                    'count' => count($websiteMentions),
                    'urls' => $websiteMentions
                ];
            }
            // Case 3: Contact from external source with website mentions
            elseif ($isExternalSource && !$isFromWebsiteCrawl) {
                $context->contacts[$index]['cross_references'] = [
                    'type' => 'external_validated',
                    'count' => count($websiteMentions),
                    'urls' => $websiteMentions
                ];
            }
            // Case 4: Contact originally from website crawl, enriched via external source
            elseif ($isExternalSource && $isFromWebsiteCrawl) {
                $context->contacts[$index]['cross_references'] = [
                    'type' => 'enriched_website',
                    'count' => count($websiteMentions),
                    'urls' => $websiteMentions
                ];
            }
            // Default case: Single website mention (still add data but no boost)
            elseif ($isWebsiteSource && count($websiteMentions) == 1) {
                $context->contacts[$index]['cross_references'] = [
                    'type' => 'website_single',
                    'count' => 1,
                    'urls' => $websiteMentions
                ];
            }
        }
    }

    /**
     * Enrich contacts with public data
     *
     * @param string $domainName
     * @param PeopleFinderContext $context
     * @return void
     */
    protected function enrichPeopleWithPublicData(string $domainName, PeopleFinderContext $context): void
    {
        $enrichmentService = app()->make(EmailEnrichmentService::class, [
            'domainName' => $domainName,
            'searchId' => null,
            'enableEnrichmentApis' => false
        ]);

        if (!$enrichmentService->hasAvailableSources()) {
            $context->metricsCollector->recordError('enrichment', new \Exception('No enrichment sources available'));
            return;
        }

        $commonNames = commonCompanyEmailNames();

        // Use the enrichment service to get name and position if missing.
        foreach ($context->contacts as $index => $contact) {
            if (
                (empty($contact['first_name']) || empty($contact['position'])) &&
                !empty($contact['email']) &&
                !in_array(strtolower(explode('@', $contact['email'])[0]), $commonNames)
            ) {
                try  {
                    $enrichedContact = $enrichmentService->searchGoogle(['email' => $contact['email']]);
                    $enriched = false;

                    // Filter out unnecessary fields and prepare data for addContactWithMetadata
                    $filteredContact = [
                        'email' => $contact['email'],
                        'source' => $contact['source'] ?? 'pf',
                        'source_url' => $contact['source_url'] ?? null,
                        'extraction_method' => $contact['extraction_method'] ?? null
                    ];

                    // Only include fields that were successfully enriched
                    if (!empty($enrichedContact['first_name']) && empty($contact['first_name'])) {
                        $filteredContact['first_name'] = $enrichedContact['first_name'];
                        $enriched = true;
                    }

                    if (!empty($enrichedContact['last_name']) && empty($contact['last_name'])) {
                        $filteredContact['last_name'] = $enrichedContact['last_name'];
                        $enriched = true;
                    }

                    if (!empty($enrichedContact['position']) && empty($contact['position'])) {
                        $filteredContact['position'] = $enrichedContact['position'];
                        $enriched = true;
                    }

                    if (!empty($enrichedContact['linkedin']) && empty($contact['linkedin'])) {
                        $filteredContact['linkedin'] = $enrichedContact['linkedin'];
                        $enriched = true;
                    }

                    // Only update if we actually enriched something
                    if ($enriched) {
                        $filteredContact['enrichment_source'] = $enrichedContact['enrichment_source'] ?? 'pf_google';
                        $context->addContactWithMetadata($filteredContact);
                    }

                    $context->metricsCollector->addEnrichmentQueryMetrics($enrichedContact);

                } catch (\Throwable $e) {
                    $context->metricsCollector->recordError('enrichment', $e);
                }
            }
        }
    }

    protected function verifyEmails($context): void
    {
        // Extract all emails from contacts
        $emails = collect($context->contacts)
            ->filter(fn($contact) => !empty($contact['email']))
            ->pluck('email')
            ->unique()
            ->values()
            ->toArray();

        if (empty($emails)) {
            return;
        }

        // Verify all emails in batch using the same proxy (all emails belong to $context->domain)
        $verificationResults = app(EmailVerifierService::class)
            ->setIntegration('reacher')
            ->verifyEmail($emails, 'pf', $context->domain);

        // Update contacts with verification results
        foreach ($context->contacts as $index => $contact) {
            if (empty($contact['email'])) {
                continue;
            }

            $verifyResult = collect($verificationResults)
                ->firstWhere('email', $contact['email']);

            if ($verifyResult) {
                $context->contacts[$index]['verified_at'] = now()->timestamp;
                $context->contacts[$index]['verification_valid'] = $verifyResult['valid'] ?? false;
                $context->contacts[$index]['verification_error'] = $verifyResult['error'] ?? null;
            }
        }
    }

    /**
     * Format contacts for return
     *
     * @param PeopleFinderContext $context
     * @return array
     */
    protected function formatContactsForReturn(PeopleFinderContext $context): array
    {
        $people = [];

        foreach ($context->contacts as $contact) {
            $person = [
                'email' => $contact['email'],
                'first_name' => $contact['first_name'],
                'last_name' => $contact['last_name'],
                'position' => $contact['position'],
                'source' => $contact['source'] ?? 'pf',
                'source_url' => $contact['source_url'],
                'enrichment_source' => $contact['enrichment_source'],
                'email_source' => $contact['email_source'],
                'phone' => $contact['phone'],
                'country' => $contact['country'],
                'address' => $contact['address'],
                'linkedin' => $contact['linkedin'],
                'social' => $contact['social'],
                'confidence_score' => $contact['confidence_score'],
                'verified_at' => $contact['verified_at'],
                'verified_valid' => $contact['verification_valid'],
                'verification_error' => $contact['verification_error'] ?? null,
            ];

            $people[] = $person;
        }

        return $people;
    }

    /**
     * Check if the context has any executive-level contacts
     *
     * @param PeopleFinderContext $context The context containing contacts
     * @return bool True if any executives are found
     */
    protected function hasExecutives(PeopleFinderContext $context): bool
    {
        foreach ($context->contacts as $contact) {
            $position = $contact['title'] ?? '';
            $rank = $this->seniorityRanker->determineSeniority($position);
            if ($rank >= 60) {
                return true;
            }
        }
        return false;
    }

    // Standardize results to all required fields
    protected function standardizePerson(array $raw): array
    {
        return [
            'email' => $raw['email'] ?? null,
            'first_name' => $raw['first_name'] ?? null,
            'last_name' => $raw['last_name'] ?? null,
            'phone' => $raw['phone'] ?? null,
            'country' => $raw['country'] ?? null,
            'address' => $raw['address'] ?? null,
            'source' => $raw['source'] ?? null,
            'source_url' => $raw['source_url'] ?? null,
            'enrichment_source' => $raw['enrichment_source'] ?? null,
            'position' => $raw['position'] ?? null,
            'linkedin' => $raw['linkedin'] ?? null,
            'social' => $raw['social'] ?? [],
        ];
    }

    protected function isCompanyMentioned(string $text, string $companyName, string $domain): bool
    {
        $text = strtolower($text);
        $companyName = strtolower($companyName);
        $domain = strtolower($domain);

        // Always accept if the domain appears in the text
        if (str_contains($text, $domain)) {
            return true;
        }

        // Accept if company name appears in any basic affiliation phrasing
        $phrases = [
            "at $companyName",
            "with $companyName",
            "from $companyName",
            "for $companyName",
            "joined $companyName",
            "$companyName's",
            "@ $companyName",
            "currently at $companyName",
            "works at $companyName"
        ];

        foreach ($phrases as $phrase) {
            if (str_contains($text, $phrase)) {
                return true;
            }
        }

        // Loose fallback match
        if (str_contains($text, $companyName)) {
            return true;
        }

        return false;
    }

    /**
     * Clean company name by standardizing format and removing legal suffixes
     */
    protected function cleanCompanyName(string $input): string
    {
        $name = trim($input);

        // Remove common legal suffixes
        $suffixes = [
            ' Inc\.?$', ' LLC\.?$', ' Ltd\.?$', ' Limited$', ' Corp\.?$', ' Corporation$',
            ' GmbH$', ' Co\.?$', ' Company$', ' S\.A\.$', ' P\.C\.$', ' LLP\.?$'
        ];

        foreach ($suffixes as $suffix) {
            $name = preg_replace('/' . $suffix . '/i', '', $name);
        }

        return trim($name);
    }

    /**
     * Validate that we have the required inputs
     */
    protected function validateInputs(?string $domainName, ?string $companyName): void
    {
        if (empty($domainName)) {
            throw new \InvalidArgumentException('Could not determine domain name');
        }

        if (empty($companyName)) {
            // Log warning but continue - we might still find people with just the domain
            \Illuminate\Support\Facades\Log::warning("No company name found for domain {$domainName}");
        }
    }

    /**
     * Check if a URL is from a search engine
     */
    protected function isSearchEngine(string $url): bool
    {
        $searchEngines = ['google.com', 'bing.com', 'yahoo.com', 'duckduckgo.com', 'baidu.com'];
        foreach ($searchEngines as $engine) {
            if (strpos($url, $engine) !== false) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if a URL is from a social media site
     */
    protected function isSocialMedia(string $url): bool
    {
        $socialSites = ['facebook.com', 'linkedin.com', 'twitter.com', 'instagram.com', 'pinterest.com'];
        foreach ($socialSites as $site) {
            if (strpos($url, $site) !== false) {
                return true;
            }
        }
        return false;
    }
}
