<?php

namespace App\Services;

use App\Models\EmailVerification;
use App\Models\EmailVerifierProxy;
use GuzzleHttp\Client as HttpClient;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class EmailVerifierService
{
    private string $integration;
    private ProxyManagerService $proxyManager;

    public function __construct($integration = 'go-verifier')
    {
        $this->integration = $integration;
        $this->proxyManager = app(ProxyManagerService::class);
    }

    public function setIntegration(string $integration): self
    {
        $this->integration = $integration;
        return $this;
    }

    /**
     * Main entry point - accepts single email or array of emails
     *
     * @param string|array $emailOrEmails
     * @param string|null $source
     * @param string|null $domain Domain for proxy selection (extracted from first email if not provided)
     * @return array|array[]
     */
    public function verifyEmail($emailOrEmails, $source = null, $domain = null)
    {
        // Handle single email
        if (is_string($emailOrEmails)) {
            return $this->verifySingleEmail($emailOrEmails, $source, $domain);
        }

        // Handle array of emails
        if (is_array($emailOrEmails)) {
            return $this->verifyBatchEmails($emailOrEmails, $source, $domain);
        }

        throw new \InvalidArgumentException('Email input must be string or array');
    }

    /**
     * Verify a single email
     */
    private function verifySingleEmail(string $email, ?string $source, ?string $domain): array
    {
        if ($this->integration === 'go-verifier') {
            return $this->goVerification($email, $source);
        }

        if ($this->integration === 'reacher') {
            $extractedDomain = $domain ?: $this->extractDomain($email);
            return $this->reacherVerification($email, $source, $extractedDomain);
        }

        throw new \InvalidArgumentException("Unsupported integration: {$this->integration}");
    }

    /**
     * Verify batch of emails using same proxy
     */
    private function verifyBatchEmails(array $emails, ?string $source, ?string $domain): array
    {
        if (empty($emails)) {
            return [];
        }

        // Extract domain from first email if not provided
        $targetDomain = $domain ?: $this->extractDomain($emails[0]);

        $results = [];

        if ($this->integration === 'go-verifier') {
            // Go-verifier doesn't use proxies, verify each individually
            foreach ($emails as $email) {
                $results[] = $this->goVerification($email, $source);
            }
        } elseif ($this->integration === 'reacher') {
            // Reacher with shared proxy for the batch
            $proxy = $this->proxyManager->getProxyForDomain($targetDomain);

            foreach ($emails as $email) {
                $results[] = $this->reacherVerification($email, $source, $targetDomain, $proxy);
            }
        }

        return $results;
    }

    /**
     * Extract domain from email address
     */
    private function extractDomain(string $email): string
    {
        return Str::after($email, '@');
    }

    /**
     * Verify an email address using GoVerifier local API-server
     * @param string $email The email address to verify
     * @return array{
     *     email: string,
     *     valid: bool, // true if the email is deliverable
     *     verification: bool, // true if verification was completed
     *     error: string|null,
     *     raw_error: string|null
     * }
     */
    public function goVerification($email, $source = null): array
    {
        $http = new HttpClient(['base_uri' => config('app.goVerifier.url')]);
        $username = Str::before($email, '@');
        $domain = Str::after($email, '@');

        $valid = null;
        $error = null;

        try {
            $response = $http->get("smtp-verify/$username/$domain");
            $results = json_decode($response->getBody()->getContents(), true);
//                $status = $response->getStatusCode();

            if ($results['deliverable']) {
                $valid = true;
                $verificationError = null;
                $error = null;
            } elseif (!$results['deliverable'] && $results['catch_all']) {
                $valid = false;
                $error = 'catch-all';
            } elseif (!$results['deliverable'] && !$results['catch_all']) {
                $valid = false;
                $error = 'invalid';
            }
            $verifyResults = [
                'email' => $email,
                'valid' => $valid,
                'verification' => true,
                'error' => $error,
                'raw_error' => null,
                'source' => $source,
                'verification_method' => 'go-verifier',
            ];
        } catch (\GuzzleHttp\Exception\ServerException $e) {
//                $error = $e->getMessage();
            $response = $e->getResponse();
            $status = $response->getStatusCode();
            $result = $response->getBody()->getContents();
            // Cases of unable to verify
            if (Str::contains($result, 'Mail server does not exist')) {
                $valid = false;
                $verification = true;
                $error = 'server-not-exist';
            } elseif (Str::contains($result, 'server misbehaving')) {
                $valid = false;
                $verification = true;
                $error = 'server-misbehaving';
            } elseif (Str::contains($result, 'Mail server is unavailable') && Str::contains($result, 'policy')) {
                $valid = true;
                $verification = false;
                $error = 'policy-restriction';
            } elseif (Str::contains($result, 'timed out')) {
                $valid = true;
                $verification = false;
                $error = 'timeout';
            } elseif (Str::contains($result, 'Blocked')) {
                $valid = true;
                $verification = false;
                $error = 'blocked';
            } else {
                $valid = true;
                $verification = false;
                $error = 'unknown';
            }
            $rawError = Str::limit("$status: $result", 250);

            $verifyResults = [
                'email' => $email,
                'valid' => $valid,
                'verification' => $verification,
                'error' => $error,
                'raw_error' => $rawError,
                'source' => $source,
                'verification_method' => 'go-verifier',
            ];

        } catch (\Throwable $e) {
            $verifyResults = [
                'email' => $email,
                'valid' => false,
                'verification' => false,
                'error' => 'api-error',
                'raw_error' => Str::limit($e->getMessage(), 250),
                'source' => $source,
                'verification_method' => 'go-verifier',
            ];
        }

        EmailVerification::create($verifyResults);

        return $verifyResults;
    }

    /**
     * Verify email using Reacher API with proxy support
     */
    public function reacherVerification($email, $source = null, $domain = null, EmailVerifierProxy $proxy = null): array
    {
        // Get proxy if not provided
        if (!$proxy) {
            $targetDomain = $domain ?: $this->extractDomain($email);
            $proxy = $this->proxyManager->getProxyForDomain($targetDomain);
        }

        $http = new HttpClient(['base_uri' => config('app.reacher.url')]);

        try {
            $requestData = [
                'json' => [
                    'to_email' => $email,
                ]
            ];

            // Add proxy configuration if available
            if ($proxy) {
                $proxyConfig = $proxy->getConfigArray();
                $requestData['json']['proxy'] = $proxyConfig;
                $requestData['json']['from_email'] = 'noreply@' . $proxy->domain; // Use proxy host as from_email
                $requestData['json']['hello_name'] = $proxyConfig['host']; // Use proxy host as hello_name

                info("Reacher verification with proxy: {$proxy->id} for email: $email", $requestData['json']);
            }

            info('email verification... doing reacher post request');
            $response = $http->post('check_email', $requestData);
            $results = json_decode($response->getBody()->getContents(), true);
            // log reacher response
            info("Reacher verification response for email: $email", $results);

            // Map Reacher response to our standard format
            $verifyResults = $this->mapReacherResponse($results, $email, $source, $proxy);

        } catch (\GuzzleHttp\Exception\ClientException $e) {
            // Handle 4xx errors
            $verifyResults = $this->handleReacherError($e, $email, $source, $proxy, 'client-error');
        } catch (\GuzzleHttp\Exception\ServerException $e) {
            // Handle 5xx errors
            $verifyResults = $this->handleReacherError($e, $email, $source, $proxy, 'server-error');
        } catch (\Throwable $e) {
            // Handle other errors
            $verifyResults = [
                'email' => $email,
                'valid' => false,
                'verification' => false,
                'error' => 'api-error',
                'raw_error' => Str::limit($e->getMessage(), 250),
                'source' => $source,
                'verification_method' => 'reacher',
                'proxy_id' => $proxy?->id,
            ];

            if ($proxy) {
                $this->proxyManager->markProxyAsProblematic($proxy, 'API error: ' . $e->getMessage());
            }
        }

        EmailVerification::create($verifyResults);

        return $verifyResults;
    }

    /**
     * Map Reacher API response to our standard format
     */
    private function mapReacherResponse(array $results, string $email, ?string $source, ?EmailVerifierProxy $proxy): array
    {
        $isReachable = $results['is_reachable'] ?? 'unknown';
        $smtpData = $results['smtp'] ?? [];
        $isDeliverable = $smtpData['is_deliverable'] ?? false;

        // Determine validity based on Reacher's response
        $valid = false;
        $error = null;

        if ($isReachable === 'safe' && $isDeliverable) {
            $valid = true;
        } elseif ($isReachable === 'risky') {
            $valid = true; // Risky but potentially valid
            $error = 'risky';
        } elseif ($isReachable === 'invalid') {
            $valid = false;
            $error = 'invalid';
        } elseif ($smtpData['is_catch_all'] ?? false) {
            $valid = false;
            $error = 'catch-all';
        } else {
            $valid = false;
            $error = 'unknown';
        }

        return [
            'email' => $email,
            'valid' => $valid,
            'verification' => true,
            'error' => $error,
            'raw_error' => null,
            'source' => $source,
            'verification_method' => 'reacher',
            'proxy_id' => $proxy?->id,
            // Store rich Reacher data
            'mx_data' => $results['mx'] ?? null,
            'smtp_data' => $results['smtp'] ?? null,
            'syntax_data' => $results['syntax'] ?? null,
            'misc_data' => $results['misc'] ?? null,
            'debug_data' => $results['debug'] ?? null,
        ];

        /*
         * Example of reacher results:
        {
            "input": "<EMAIL>",
            "is_reachable": "safe",
            "misc": {
                "is_disposable": false,
                "is_role_account": false,
                "is_b2c": false,
                "gravatar_url": null,
                "haveibeenpwned": null
            },
            "mx": {
                "accepts_mail": true,
                "records": ["ASPMX.L.GOOGLE.com.", "ALT2.ASPMX.L.GOOGLE.com.", "ALT1.ASPMX.L.GOOGLE.com.", "ALT3.ASPMX.L.GOOGLE.com.", "ALT4.ASPMX.L.GOOGLE.com.", "inbound-smtp.us-east-1.amazonaws.com."]
            },
            "smtp": {
                "can_connect_smtp": true,
                "has_full_inbox": false,
                "is_catch_all": false,
                "is_deliverable": true,
                "is_disabled": false
            },
            "syntax": {
                "address": "<EMAIL>",
                "domain": "nutridyn.com",
                "is_valid_syntax": true,
                "username": "joel",
                "normalized_email": "<EMAIL>",
                "suggestion": null
            },
            "debug": {
                "backend_name": "backend-dev",
                "start_time": "2025-06-09T13:20:05.927756141Z",
                "end_time": "2025-06-09T13:20:08.707473931Z",
                "duration": {
                    "secs": 2,
                    "nanos": *********
                },
                "smtp": {
                    "verif_method": {
                        "type": "Smtp",
                        "host": "ASPMX.L.GOOGLE.com.",
                        "verif_method": {
                            "from_email": "<EMAIL>",
                            "hello_name": "mail.icewp.com",
                            "proxy": "default",
                            "smtp_port": 25,
                            "smtp_timeout": null,
                            "retries": 1
                        }
                    }
                }
            }
        },
        {
            "input": "<EMAIL>",
            "is_reachable": "unknown",
            "misc": {
                "is_disposable": false,
                "is_role_account": true,
                "is_b2c": false,
                "gravatar_url": null,
                "haveibeenpwned": null
            },
            "mx": {
                "accepts_mail": true,
                "records": ["mx1-us1.ppe-hosted.com.", "mx2-us1.ppe-hosted.com."]
            },
            "smtp": {
                "error": {
                    "type": "AsyncSmtpError",
                    "message": "permanent: 5.7.1 Service unavailable; client [*************] blocked using Proofpoint Dynamic Reputation (Visit https://ipcheck.proofpoint.com/ if you feel this is in error.). Please provide the following IP address when reporting problems: *************"
                },
                "description": "IpBlacklisted"
            },
            "syntax": {
                "address": "<EMAIL>",
                "domain": "mybiofusion.com",
                "is_valid_syntax": true,
                "username": "frontdesk",
                "normalized_email": "<EMAIL>",
                "suggestion": null
            },
            "debug": {
                "backend_name": "backend-dev",
                "start_time": "2025-06-09T13:20:30.995589848Z",
                "end_time": "2025-06-09T13:20:38.859033376Z",
                "duration": {
                    "secs": 7,
                    "nanos": *********
                },
                "smtp": {
                    "verif_method": {
                        "type": "Smtp",
                        "host": "mx1-us1.ppe-hosted.com.",
                        "verif_method": {
                            "from_email": "<EMAIL>",
                            "hello_name": "mail.icewp.com",
                            "proxy": "default",
                            "smtp_port": 25,
                            "smtp_timeout": null,
                            "retries": 1
                        }
                    }
                }
            }
        }
         */
    }

    /**
     * Handle Reacher API errors
     */
    private function handleReacherError(\Exception $e, string $email, ?string $source, ?EmailVerifierProxy $proxy, string $errorType): array
    {
        $response = $e->getResponse();
        $status = $response ? $response->getStatusCode() : 0;
        $result = $response ? $response->getBody()->getContents() : $e->getMessage();

        $rawError = Str::limit("$status: $result", 250);

        // Mark proxy as problematic if error seems proxy-related
        if ($proxy && ($status >= 500 || $status === 407 || $status === 0)) {
            $this->proxyManager->markProxyAsProblematic($proxy, "HTTP $status error", [
                'email' => $email,
                'response' => $rawError
            ]);
        }

        return [
            'email' => $email,
            'valid' => false,
            'verification' => false,
            'error' => $errorType,
            'raw_error' => $rawError,
            'source' => $source,
            'verification_method' => 'reacher',
            'proxy_id' => $proxy?->id,
        ];
    }
}
