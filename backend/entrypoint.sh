#!/bin/bash

if [ "$(id -u)" -eq 0 ]; then
    echo "Running as root: NOT setting iptables..."

    iptables -F
    iptables -P OUTPUT DROP

    # Essential rules:
    iptables -A OUTPUT -o lo -j ACCEPT                    # Loopback
    iptables -A OUTPUT -d **********/16 -j ACCEPT        # Docker network

    # Your proxy servers on port 4481:
    iptables -A OUTPUT -d ************* -p tcp --dport 4481 -j ACCEPT
    iptables -A OUTPUT -d ************* -p tcp --dport 4481 -j ACCEPT

    # DNS resolution (needed to resolve mail.icewp.com to *************):
    iptables -A OUTPUT -p udp --dport 53 -j ACCEPT
    iptables -A OUTPUT -p tcp --dport 53 -j ACCEPT

    # Start monitoring as root in background
    echo "Starting network monitoring as root..."
    while true; do
        echo "=== $(date) ===" >> /tmp/connections.log
        netstat -tupon >> /tmp/connections.log
        echo "" >> /tmp/connections.log
        sleep 2
    done &

    echo "Switching to chrome user..."
    exec su chrome -c "$0"   # Re-exec this script as chrome
fi

# Now running as "chrome"
#chromedriver &
#exec ./reacher_backend

# Now running as "chrome"
echo "Starting chromedriver..."
chromedriver --bind-address=0.0.0.0 --port=9515 &

echo "Starting reacher_backend..."
./reacher_backend &

# Keep container alive
wait
