# From https://shaneutt.com/blog/rust-fast-small-docker-image-builds/

# ------------------------------------------------------------------------------
# Cargo Build Stage
# ------------------------------------------------------------------------------

FROM messense/rust-musl-cross:x86_64-musl as builder

WORKDIR /usr/src/reacher

RUN rm -f target/x86_64-unknown-linux-musl/release/deps/reacher*

COPY . .

ENV SQLX_OFFLINE=true

RUN cargo build --bin reacher_backend --release --target=x86_64-unknown-linux-musl

# ------------------------------------------------------------------------------
# Final Stage
# ------------------------------------------------------------------------------

FROM debian:bullseye-slim

# Install dependencies: Chrome, chromedriver, iptables, etc.
RUN apt-get update && apt-get install -y \
    chromium chromium-driver iptables ca-certificates curl \
    net-tools iproute2 procps netcat-traditional dnsutils \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Add a user (mimicking "chrome" user from zenika image)
RUN useradd -m -s /bin/bash chrome

WORKDIR /home/<USER>/

# Copy compiled binary and config
COPY --from=builder /usr/src/reacher/target/x86_64-unknown-linux-musl/release/reacher_backend .
COPY --from=builder /usr/src/reacher/backend/backend_config.toml .
COPY --from=builder /usr/src/reacher/backend/docker.sh .

# Set permissions for stable files
RUN chmod +x docker.sh reacher_backend && \
    chown -R chrome:chrome .

# User chrome was created in zenika/alpine-chrome
# USER chrome

ENV RUST_LOG=reacher=info
ENV RCH__HTTP_HOST=0.0.0.0
# Currently this Dockerfile is mainly used for single-shot verifications, so we
# disable the worker by default.
ENV RCH__WORKER__ENABLE=false

EXPOSE 8080

COPY ./backend/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Remove entrypoint from parent Docker file
# https://stackoverflow.com/questions/40122152/how-to-remove-entrypoint-from-parent-image-on-dockerfile
ENTRYPOINT ["/entrypoint.sh"]

# CMD ["./docker.sh"]
